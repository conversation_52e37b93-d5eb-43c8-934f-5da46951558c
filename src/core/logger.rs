use std::io;

use tracing_appender::non_blocking::WorkerGuard;
use tracing_subscriber::{EnvFilter, fmt, prelude::*};

use crate::config::logger::{LogFormat, LoggerConfig};

pub struct Logger {
    _guard: Option<WorkerGuard>,
}

macro_rules! init_subscriber {
    ($config:expr, $env_filter:expr, $writer:expr) => {
        match $config.format {
            LogFormat::Json => {
                tracing_subscriber::registry()
                    .with($env_filter)
                    .with(
                        fmt::layer()
                            .with_writer($writer)
                            .json()
                            .with_target($config.show_target)
                            .with_thread_ids($config.show_thread_ids),
                    )
                    .init();
            }
            LogFormat::Pretty => {
                if $config.compact_format {
                    tracing_subscriber::registry()
                        .with($env_filter)
                        .with(
                            fmt::layer()
                                .with_writer($writer)
                                .compact()
                                .with_target($config.show_target)
                                .with_thread_ids($config.show_thread_ids),
                        )
                        .init();
                } else {
                    tracing_subscriber::registry()
                        .with($env_filter)
                        .with(
                            fmt::layer()
                                .with_writer($writer)
                                .pretty()
                                .with_target($config.show_target)
                                .with_thread_ids($config.show_thread_ids),
                        )
                        .init();
                }
            }
        }
    };
}

impl Logger {
    pub fn init(config: &LoggerConfig) -> anyhow::Result<Self> {
        let env_filter = build_env_filter(config)?;

        if config.async_logging {
            let (non_blocking, guard) = tracing_appender::non_blocking(io::stdout());
            init_subscriber!(config, env_filter, non_blocking);
            Ok(Self { _guard: Some(guard) })
        } else {
            init_subscriber!(config, env_filter, io::stdout);
            Ok(Self { _guard: None })
        }
    }
}

fn build_env_filter(config: &LoggerConfig) -> anyhow::Result<EnvFilter> {
    let filter = if let Some(ref module_filters) = config.module_filters {
        EnvFilter::try_new(module_filters).map_err(|e| anyhow::anyhow!("Invalid module filters: {}", e))?
    } else if let Ok(env_filter) = std::env::var("RUST_LOG") {
        EnvFilter::try_new(&env_filter).map_err(|e| anyhow::anyhow!("Invalid RUST_LOG: {}", e))?
    } else {
        EnvFilter::new(format!("shreder={}", config.level.to_lowercase()))
    };

    Ok(filter)
}
