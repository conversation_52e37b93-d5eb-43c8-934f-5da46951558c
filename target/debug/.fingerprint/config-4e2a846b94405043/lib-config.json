{"rustc": 8210029788606052455, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 18330098564635666122, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 7052233144955192300], [1238778183371849706, "yaml_rust2", false, 10742400036605282231], [2244620803250265856, "ron", false, 3213138774701516437], [2356429411733741858, "ini", false, 12774955983688215185], [6517602928339163454, "path<PERSON><PERSON>", false, 17391512722256634250], [9689903380558560274, "serde", false, 6499116197200794356], [11946729385090170470, "async_trait", false, 13205257525280736366], [13475460906694513802, "convert_case", false, 9992150549713752844], [14718834678227948963, "winnow", false, 1126674785273598766], [15367738274754116744, "serde_json", false, 17322629564997694187], [15609422047640926750, "toml", false, 3038417028041536315]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-4e2a846b94405043/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}