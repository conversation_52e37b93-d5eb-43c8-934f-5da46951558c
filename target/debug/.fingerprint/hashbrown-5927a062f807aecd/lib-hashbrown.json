{"rustc": 8210029788606052455, "features": "[\"default-hasher\", \"inline-more\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 5347358027863023418, "path": 6057324441630989246, "deps": [[10842263908529601448, "<PERSON><PERSON><PERSON>", false, 12604042973588985744]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-5927a062f807aecd/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}