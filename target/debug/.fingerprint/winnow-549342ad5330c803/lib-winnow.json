{"rustc": 8210029788606052455, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 18007145932999333080, "path": 3622169010033162278, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-549342ad5330c803/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}