{"rustc": 8210029788606052455, "features": "[]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 5347358027863023418, "path": 5563409906317325421, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-41b20dd4a208dd26/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}